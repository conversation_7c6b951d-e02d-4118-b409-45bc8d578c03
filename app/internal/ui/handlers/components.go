// Package handlers contains UI handlers including reusable components for conditional buttons.
//
// Conditional Button Pattern:
// Use ConditionalButtonWithContainer in templates and RenderConditionalButtonState in handlers
// to create buttons that are enabled/disabled based on dropdown selections.
//
// Template usage:
//
//	@ConditionalButtonWithContainer(
//	    "my-button-container", "my-btn", "Submit", "btn btn-primary",
//	    "/ui-api/submit", "#result", "my-dropdown", "/ui-api/my-button-state",
//	    "form-group mt-4", false,
//	)
//
// Handler usage:
//
//	func (h *HandlerService) MyButtonState(w http.ResponseWriter, r *http.Request) {
//	    params := ConditionalButtonStateParams{
//	        ButtonID: "my-btn", ButtonText: "Submit", ButtonClass: "btn btn-primary",
//	        HxPost: "/ui-api/submit", HxTarget: "#result",
//	        DependsOnDropdown: "my-dropdown", FormFieldName: "my-dropdown",
//	    }
//	    h.RenderConditionalButtonState(w, r, params)
//	}
//
// Route: r.Get("/ui-api/my-button-state", handlerService.MyButtonState)
package handlers

import (
	"log/slog"
	"net/http"

	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/templates"
)

func (h *HandlerService) ClearAlerts(w http.ResponseWriter, r *http.Request) {
	component := templates.ClearAlerts()
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render ClearAlerts", slog.String("error", err.Error()))
	}
}

// ConditionalButtonStateParams holds the parameters for rendering a conditional button
type ConditionalButtonStateParams struct {
	ButtonID          string
	ButtonText        string
	ButtonClass       string
	HxPost            string
	HxTarget          string
	DependsOnDropdown string
	FormFieldName     string // The form field name to check for enabling the button
}

// RenderConditionalButtonState is a generic handler for conditional button state updates
// It checks if the specified form field has a value and renders the button accordingly
func (h *HandlerService) RenderConditionalButtonState(w http.ResponseWriter, r *http.Request, params ConditionalButtonStateParams) {
	fieldValue := r.FormValue(params.FormFieldName)
	isEnabled := fieldValue != ""

	component := templates.ConditionalButton(
		params.ButtonID,
		params.ButtonText,
		params.ButtonClass,
		params.HxPost,
		params.HxTarget,
		"#"+params.DependsOnDropdown,
		params.DependsOnDropdown,
		isEnabled,
	)

	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render conditional button state", slog.String("error", err.Error()))
	}
}
