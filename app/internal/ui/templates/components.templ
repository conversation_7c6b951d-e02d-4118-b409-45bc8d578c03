
package templates
import (
    "strings"
    "github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)


// the list of available signal types for the selected ISN
templ SignalTypeOptions(signalTypes []types.SignalTypeOption) {
	<select
		id="signal-type-slug"
		name="signal-type-slug"
		required
		hx-post="/ui-api/signal-type-version-options"
		hx-target="#sem-ver"
		hx-swap="outerHTML"
		hx-trigger="change"
		hx-include="#isn-slug, this"
		class="form-select"
	>
		<option value="">Select Signal Type...</option>
		for _, signalType := range signalTypes {
			<option value={ signalType.Slug }>{ strings.ReplaceAll(signalType.Slug, "-", " ") }</option>
		}
	</select>
}

templ ServiceAccountOptions(serviceAccounts []types.ServiceAccountOption) {
	<select
		id="service-account-dropdown"
		name="service-account-dropdown"
		required
		class="form-select"
	>
		<option value="">Select Service Account...</option>
		for _, account := range serviceAccounts {
			<option value={ account.ClientOrganization + "|" + account.ClientContactEmail }>{ account.ClientOrganization } ({ account.ClientContactEmail })</option>
		}
	</select>
}

// the list of available versions for the selected signal type
templ SignalTypeVersionOptions(versions []types.VersionOption) {
	<select
		id="sem-ver"
		name="sem-ver"
		required
		class="form-select"
	>
		<option value="">Select Version...</option>
		for _, version := range versions {
			<option value={ version.Version }>{ version.Version }</option>
		}
	</select>
}

templ WarningAlert(message string) {
	<div class="alert alert-warning">
		<svg aria-hidden="true" focusable="false" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" display="inline-block" overflow="visible" style="vertical-align: text-bottom;"><path d="M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>
		{ message }
	</div>
}

templ ErrorAlert(message string) {
	<div class="alert alert-error">
		{ message }
	</div>
}

templ SuccessAlert(message string) {
	<div class="alert alert-success">
		{ message }
	</div>
}

templ ClearAlerts() {
	""
}

templ SignalMetadataItem(label, value string) {
	<div class="signal-metadata-item">
		<span class="signal-metadata-label">{ label }:</span>
		<div class="signal-metadata-value">{ value }</div>
	</div>
}

templ SignalMetadataSimple(label, value string) {
	<div class="signal-metadata-item">
		<span class="signal-metadata-label">{ label }:</span> { value }
	</div>
}

templ CheckboxField(name, value, label string) {
	<label class="checkbox-field">
		<input type="checkbox" name={ name } value={ value } class="checkbox-input"/>
		<span class="text-sm">{ label }</span>
	</label>
}

// ConditionalButton renders a button that is enabled/disabled based on a dropdown selection
// Parameters:
// - id: unique ID for the button
// - text: button text
// - cssClass: CSS classes for the button
// - hxPost: htmx POST endpoint
// - hxTarget: htmx target selector
// - hxInclude: htmx include selector (usually the dropdown)
// - dependsOnDropdown: ID of the dropdown this button depends on
// - isEnabled: whether the button should start enabled
templ ConditionalButton(id, text, cssClass, hxPost, hxTarget, hxInclude, dependsOnDropdown string, isEnabled bool) {
	<button
		id={ id }
		hx-post={ hxPost }
		hx-target={ hxTarget }
		hx-include={ hxInclude }
		class={ cssClass }
		disabled?={ !isEnabled }
	>
		{ text }
	</button>
}

// ConditionalButtonStateUpdater creates a hidden div that updates button state when dropdown changes
// This should be used alongside ConditionalButton to create the complete functionality
// Parameters:
// - buttonContainerID: ID of the container holding the button
// - dependsOnDropdown: ID of the dropdown that triggers the state change
// - stateEndpoint: endpoint that returns the updated button HTML
templ ConditionalButtonStateUpdater(buttonContainerID, dependsOnDropdown, stateEndpoint string) {
	<div hx-get={ stateEndpoint }
		hx-trigger={ "change from:#" + dependsOnDropdown }
		hx-target={ "#" + buttonContainerID }
		hx-swap="innerHTML"
		hx-include={ "#" + dependsOnDropdown }
		style="display: none;">
	</div>
}

// ConditionalButtonWithContainer is a complete solution that includes both the button container and state updater
// This is the easiest way to add a conditional button - just call this one component
//
// Example usage:
//   @ConditionalButtonWithContainer(
//       "delete-button-container",     // containerID
//       "delete-btn",                  // buttonID
//       "Delete Selected",             // buttonText
//       "btn btn-danger",              // buttonClass
//       "/ui-api/delete-items",        // hxPost
//       "#result-area",                // hxTarget
//       "item-selector",               // dependsOnDropdown (dropdown ID)
//       "/ui-api/delete-button-state", // stateEndpoint
//       "form-group mt-4",             // containerClass
//       false,                         // isEnabled (initial state)
//   )
//
// Parameters:
// - containerID: unique ID for the button container
// - buttonID: unique ID for the button
// - buttonText: text to display on the button
// - buttonClass: CSS classes for the button
// - hxPost: htmx POST endpoint
// - hxTarget: htmx target selector
// - dependsOnDropdown: ID of the dropdown this button depends on
// - stateEndpoint: endpoint that returns updated button state
// - containerClass: CSS classes for the container (optional, defaults to "form-group mt-4")
// - isEnabled: whether the button should start enabled
templ ConditionalButtonWithContainer(containerID, buttonID, buttonText, buttonClass, hxPost, hxTarget, dependsOnDropdown, stateEndpoint, containerClass string, isEnabled bool) {
	if containerClass == "" {
		<div id={ containerID } class="form-group mt-4">
			@ConditionalButton(
				buttonID,
				buttonText,
				buttonClass,
				hxPost,
				hxTarget,
				"#" + dependsOnDropdown,
				dependsOnDropdown,
				isEnabled,
			)
		</div>
	} else {
		<div id={ containerID } class={ containerClass }>
			@ConditionalButton(
				buttonID,
				buttonText,
				buttonClass,
				hxPost,
				hxTarget,
				"#" + dependsOnDropdown,
				dependsOnDropdown,
				isEnabled,
			)
		</div>
	}
	@ConditionalButtonStateUpdater(containerID, dependsOnDropdown, stateEndpoint)
}
