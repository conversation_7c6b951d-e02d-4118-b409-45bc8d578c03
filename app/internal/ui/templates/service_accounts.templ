package templates


import (
    "github.com/information-sharing-networks/signalsd/app/internal/ui/client"
)

// ManageServiceAccounts renders the create service account page
templ ManageServiceAccounts() {
	@BaseLayout("Create New Service Account") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Manage Service Accounts</h1>

			<!-- Reissue Service Account Credentials Section -->
			<div class="card mb-6">
				<div class="card-body">
					<h3 class="card-title">Reissue Service Account Credentials</h3>
					 @WarningAlert("Warning: Reissuing credentials will revoke any existing client secrets for the service account, including any unused one-time setup URLs.")
					<p class="text-muted mb-4">Select an existing service account to reissue its credentials.</p>
					<div class="form-group">
						<label for="service-account-dropdown" class="form-label">Service Account</label>
						<select
							id="service-account-dropdown"
							hx-get="/ui-api/service-account-options"
							hx-trigger="load"
							hx-swap="outerHTML"
						>
							<!-- Service account dropdown will be loaded here -->
						</select>
					</div>

					<div id="reissue-button-container" class="form-group mt-4">
						@ConditionalButton(
							"reissue-btn",
							"Reissue Credentials",
							"btn btn-warning",
							"/ui-api/reissue-service-account",
							"#reissue-result",
							"#service-account-dropdown",
							"service-account-dropdown",
							false,
						)
					</div>

					<div id="reissue-result" class="mt-4"></div>
				</div>
			</div>

			<!-- Clear alerts when service account dropdown changes (using event filter for dynamic content) -->
			<div hx-get="/ui-api/clear-alerts"
				hx-trigger="change[event.target.id === 'service-account-dropdown'] from:body"
				hx-target="#reissue-result"
				hx-swap="innerHTML"
				style="display: none;">
			</div>

			<!-- Create New Service Account Section -->
			<div class="card mb-6">
				<div class="card-body">
					<h3 class="card-title">Create New Service Account</h3>
					<form hx-post="/ui-api/create-service-account" hx-target="#create-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="form-group">
								<label for="email" class="form-label">Contact Email</label>
								<input
									id="email"
									name="email"
									type="email"
									class="form-input"
									placeholder="Contact email for the service account"
								/>
							</div>
						</div>
						<div class="form-group">
							<label for="organization" class="form-label">Client Organization</label>
							<input
								id="organization"
								name="organization"
								type="text"
								required
								class="form-input"
								placeholder="Client organization name"
							/>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">
								Create Service Account
							</button>
						</div>
					</form>

					<div id="create-result" class="mt-4"></div>
				</div>
			</div>
		</div>


	}
}


templ ServiceAccountCreationSuccess(response client.CreateServiceAccountResponse) {
	<div class="card">
		<div class="card-body">
			@SuccessAlert("Service Account created successfully!")
			<div class="mt-4 space-y-2">
				<p><strong>Client ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.ClientID }</code></p>
				<p><strong>Account ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.AccountID.String() }</code></p>
				<p><strong>Setup URL:</strong>
					<code class="text-xs bg-gray-100 px-2 py-1 rounded block break-all">{ response.SetupURL }</code>
				</p>
			</div>
		</div>
	</div>
}

templ ServiceAccountReissueSuccess(response client.ReissueServiceAccountResponse) {
	<div class="card">
		<div class="card-body">
			@SuccessAlert("Service Account credentials reissued successfully!")
			<div class="mt-4 space-y-2">
				<p><strong>Client ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.ClientID }</code></p>
				<p><strong>New Setup URL:</strong>
					<code class="text-sm bg-gray-100 px-2 py-1 rounded block break-all">{ response.SetupURL }</code>
				</p>
				<p class="text-sm text-gray-600 mt-2">
					<strong>Note:</strong> All previous client secrets have been revoked. The owner of this account can use the link above to retrieve their new client secret (the link can only be used once and expires in 48 hours).
				</p>
			</div>
		</div>
	</div>
	<script>
		// Disable the reissue button after successful reissue
		const reissueBtn = document.getElementById('reissue-btn');
		if (reissueBtn) {
			reissueBtn.disabled = true;
			reissueBtn.classList.add('opacity-50', 'cursor-not-allowed');
			reissueBtn.textContent = 'Credentials Reissued';
		}
	</script>
}

